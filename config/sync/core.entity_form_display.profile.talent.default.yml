uuid: 5c939c40-e9aa-433b-8c5d-3d8747ddd703
langcode: en
status: true
dependencies:
  config:
    - field.field.profile.talent.field_certificates
    - field.field.profile.talent.field_cv
    - field.field.profile.talent.field_education
    - field.field.profile.talent.field_first_name
    - field.field.profile.talent.field_general_consent
    - field.field.profile.talent.field_interested_city
    - field.field.profile.talent.field_languages
    - field.field.profile.talent.field_last_name
    - field.field.profile.talent.field_linkedin
    - field.field.profile.talent.field_nationality
    - field.field.profile.talent.field_phone
    - field.field.profile.talent.field_photo
    - field.field.profile.talent.field_professional_field
    - field.field.profile.talent.field_professional_level
    - field.field.profile.talent.field_residence_place
    - field.field.profile.talent.field_return_reason
    - field.field.profile.talent.field_subscribe_jobs
    - field.field.profile.talent.field_subscribe_marketing
    - field.field.profile.talent.field_talent_profile_consent
    - image.style.talent_mobile
    - profile.type.talent
  module:
    - field_group
    - file
    - image
    - link
    - phone_number
third_party_settings:
  field_group:
    group_profile_visibility:
      children:
        - field_talent_profile_consent
      label: 'Profile visibility'
      region: content
      parent_name: ''
      weight: 0
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
    group_personal_details:
      children:
        - field_photo
        - field_first_name
        - field_last_name
        - field_phone
        - field_linkedin
      label: 'Personal details'
      region: content
      parent_name: ''
      weight: 1
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
    group_professional_details:
      children:
        - field_education
        - field_professional_field
        - field_professional_level
        - field_languages
      label: 'Professional details'
      region: content
      parent_name: ''
      weight: 2
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
    group_location:
      children:
        - field_residence_place
        - field_interested_city
        - field_return_reason
        - field_nationality
      label: Location
      region: content
      parent_name: ''
      weight: 3
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
    group_cv:
      children:
        - field_cv
      label: 'Resume (CV)'
      region: content
      parent_name: ''
      weight: 4
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
    group_certificates:
      children:
        - field_certificates
      label: 'Diplomas and certificates'
      region: content
      parent_name: ''
      weight: 5
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
    group_subscriptions:
      children:
        - field_subscribe_marketing
        - field_subscribe_jobs
      label: Subscriptions
      region: content
      parent_name: ''
      weight: 6
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
    group_general_consent:
      children:
        - field_general_consent
      label: 'General consent'
      region: content
      parent_name: ''
      weight: 7
      format_type: fieldset
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        description: ''
        required_fields: true
id: profile.talent.default
targetEntityType: profile
bundle: talent
mode: default
content:
  field_certificates:
    type: file_generic
    weight: 8
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_cv:
    type: file_generic
    weight: 7
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_education:
    type: options_select
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_first_name:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_general_consent:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_interested_city:
    type: options_select
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  field_languages:
    type: options_select
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  field_last_name:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_linkedin:
    type: link_default
    weight: 6
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_nationality:
    type: boolean_checkbox
    weight: 11
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_phone:
    type: phone_number_default
    weight: 5
    region: content
    settings:
      default_country: BG
      placeholder: 'Телефонен номер'
      phone_size: 60
      extension_size: 5
      country_selection: flag
    third_party_settings: {  }
  field_photo:
    type: image_image
    weight: 2
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: talent_mobile
    third_party_settings: {  }
  field_professional_field:
    type: options_select
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_professional_level:
    type: options_select
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_residence_place:
    type: string_textfield
    weight: 8
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_return_reason:
    type: string_textarea
    weight: 10
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_subscribe_jobs:
    type: boolean_checkbox
    weight: 9
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_subscribe_marketing:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_talent_profile_consent:
    type: boolean_checkbox
    weight: 1
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden:
  is_default: true
