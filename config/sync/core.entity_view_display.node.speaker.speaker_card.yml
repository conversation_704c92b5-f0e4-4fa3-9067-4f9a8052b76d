uuid: 67aeb743-04e0-4297-b935-ffcbd4f06107
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.speaker_card
    - field.field.node.speaker.field_formatted_description
    - field.field.node.speaker.field_image_media
    - field.field.node.speaker.field_job_position
    - node.type.speaker
  module:
    - text
    - user
id: node.speaker.speaker_card
targetEntityType: node
bundle: speaker
mode: speaker_card
content:
  field_formatted_description:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_image_media:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: square
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_job_position:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  langcode: true
  links: true
  node_read_time: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
