uuid: f9313960-f7cf-4f6a-8deb-cc1ed3c20d1f
langcode: en
status: true
dependencies:
  config:
    - field.field.profile.talent.field_certificates
    - field.field.profile.talent.field_cv
    - field.field.profile.talent.field_education
    - field.field.profile.talent.field_first_name
    - field.field.profile.talent.field_general_consent
    - field.field.profile.talent.field_interested_city
    - field.field.profile.talent.field_languages
    - field.field.profile.talent.field_last_name
    - field.field.profile.talent.field_linkedin
    - field.field.profile.talent.field_nationality
    - field.field.profile.talent.field_phone
    - field.field.profile.talent.field_photo
    - field.field.profile.talent.field_professional_field
    - field.field.profile.talent.field_professional_level
    - field.field.profile.talent.field_residence_place
    - field.field.profile.talent.field_return_reason
    - field.field.profile.talent.field_subscribe_jobs
    - field.field.profile.talent.field_subscribe_marketing
    - field.field.profile.talent.field_talent_profile_consent
    - profile.type.talent
    - responsive_image.styles.talent
  module:
    - file
    - link
    - phone_number
    - responsive_image
id: profile.talent.default
targetEntityType: profile
bundle: talent
mode: default
content:
  field_certificates:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 13
    region: content
  field_cv:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 12
    region: content
  field_education:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_first_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_interested_city:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 5
    region: content
  field_languages:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 10
    region: content
  field_last_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 4
    region: content
  field_linkedin:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 9
    region: content
  field_phone:
    type: phone_number_international
    label: hidden
    settings:
      as_link: false
    third_party_settings: {  }
    weight: 8
    region: content
  field_photo:
    type: responsive_image
    label: hidden
    settings:
      responsive_image_style: talent
      image_link: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
  field_professional_field:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 2
    region: content
  field_professional_level:
    type: entity_reference_label
    label: above
    settings:
      link: false
    third_party_settings: {  }
    weight: 11
    region: content
  field_residence_place:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 6
    region: content
  field_return_reason:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 7
    region: content
hidden:
  field_general_consent: true
  field_nationality: true
  field_subscribe_jobs: true
  field_subscribe_marketing: true
  field_talent_profile_consent: true
  invite_link: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
