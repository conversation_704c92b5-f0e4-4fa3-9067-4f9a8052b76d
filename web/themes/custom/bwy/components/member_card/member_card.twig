{#
/**
 * @file
 * Member card component.
 */
#}

{{ attach_library('bwy/member-card') }}

{% set member_type = member_type|default('speaker') %}
{% set bg_image = member_type == 'speaker' ? 'red.png' : 'green.png' %}

<div class="group perspective-1000 w-full">
  <div class="relative w-full aspect-members-card cursor-pointer" data-flip-card>

    {# Front side #}
    <div class="card card--hover rounded-2xl absolute inset-0 py-6 flex flex-col items-center justify-end rotate-y-0 backface-hidden">
      <div class="absolute bottom-0 member-card--front bg-cover bg-no-repeat w-full h-full rounded-3xl" aria-hidden="true"></div>
      {% include '@bwy/components/cached_icon/cached_icon.twig' %}
      {% if photo %}
        <div class="w-full">
          {{ photo }}
        </div>
      {% endif %}

      {% if name or job_position %}
        <div class="body-1 flex flex-col items-center absolute bottom-2 w-48 text-center">
          {% if name %}
            <div class="font-bold">
            {{ name }}
            </div>
          {% endif %}

          {% if job_position %}
            <div class="truncate-2-lines">{{ job_position }}</div>
          {% endif %}
        </div>
      {% endif %}
    </div>

    {# Back side #}
    <div class="card card--hover rounded-2xl absolute inset-0 {{ member_type == 'speaker' ? 'p-6 md:py-52px' : 'p-6' }} flex flex-col gap-5 md:gap-6 rotate-y-180 backface-hidden text-center">
      <div class="absolute bottom-0 left-0 member-card--{{ member_type == 'speaker' ? 'red' : 'green' }} bg-cover bg-no-repeat w-full h-full rounded-3xl" aria-hidden="true"></div>
      {% include '@bwy/components/cached_icon/cached_icon.twig' %}
      {% if name or (member_type == 'team' and (phone or email)) %}
        <div class="flex flex-col gap-1 md:gap-1.5 z-1 body-2 md:body-1">
          {% if name %}
            <div class="font-bold">
            {{ name }}
            </div>
          {% endif %}
          {% if member_type == 'team' %}
            {% if phone %}
              <div class="body-3 md:body-2">
                {{ phone }}
              </div>
            {% endif %}

            {% if email %}
              <div class="body-3 md:body-2">
                {{ email }}
              </div>
            {% endif %}
          {% endif %}
        </div>
      {% endif %}
      {% if info is not empty %}
        <div class="body-3 md:body-2 z-1 overflow-y-auto scrollbar-thin">
          <p>{{ info }}</p>
        </div>
      {% endif %}
    </div>
  </div>
</div>
