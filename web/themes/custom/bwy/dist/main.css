/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-400: #ef3d43;
    --color-red-500: #f6030c;
    --color-red-700: #a60f14;
    --color-yellow-600: #e9bd10;
    --color-yellow-700: #e9b310;
    --color-green-600: #5a9f51;
    --color-blue-500: #2bbcf1;
    --color-blue-600: #2b7af1;
    --color-blue-700: #333c8c;
    --color-pink-500: #f24394;
    --color-pink-600: #cc2e77;
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wide: 0.025em;
    --leading-relaxed: 1.625;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --font-agenda: "Agenda", "Helvetica", "sans-serif";
    --font-mont: "Mont", "Helvetica", "sans-serif";
    --color-error: #eb5757;
    --color-text-main: #333138;
    --color-text-secondary: #636363;
    --color-background-main: #fafafa;
    --color-background-navbar: #1f1d25;
    --color-background-other: #e0e0e0;
    --color-border-main: #dadada;
    --color-text-main-lighter: rgba(51, 49, 56, 0.65);
    --color-steel-600: #475467;
    --color-lavender-100: #E4E3FF;
    --font-size-display-1: 58px;
    --font-size-display-1-mobile: 40px;
    --font-size-heading-1: 42px;
    --font-size-heading-1-mobile: 28px;
    --font-size-heading-2: 34px;
    --font-size-heading-2-mobile: 24px;
    --font-size-heading-3: 32px;
    --font-size-heading-3-mobile: 28px;
    --font-size-text-1: 24px;
    --font-size-text-2: 20px;
    --font-size-body-1: 18px;
    --font-size-body-2: 16px;
    --font-size-body-3: 14px;
    --font-size-button: 16px;
    --font-size-link: 14px;
    --line-height-tight: 120%;
    --line-height-normal: 150%;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .\!visible {
    visibility: visible !important;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .-top-0\.5 {
    top: calc(var(--spacing) * -0.5);
  }
  .-top-1\/12 {
    top: calc(calc(1/12 * 100%) * -1);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .-right-0\.5 {
    right: calc(var(--spacing) * -0.5);
  }
  .-right-1\/12 {
    right: calc(calc(1/12 * 100%) * -1);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-1\.5 {
    bottom: calc(var(--spacing) * 1.5);
  }
  .bottom-2 {
    bottom: calc(var(--spacing) * 2);
  }
  .bottom-4\.5 {
    bottom: 18px;
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .z-1 {
    z-index: 1;
  }
  .z-10 {
    z-index: 10;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-auto {
    z-index: auto;
  }
  .col-auto {
    grid-column: auto;
  }
  .col-span-1 {
    grid-column: span 1 / span 1;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .col-span-3 {
    grid-column: span 3 / span 3;
  }
  .col-span-9 {
    grid-column: span 9 / span 9;
  }
  .container {
    width: 100%;
    @media (width >= 640px) {
      max-width: 640px;
    }
    @media (width >= 768px) {
      max-width: 768px;
    }
    @media (width >= 1024px) {
      max-width: 1024px;
    }
    @media (width >= 1280px) {
      max-width: 1280px;
    }
    @media (width >= 1536px) {
      max-width: 1536px;
    }
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-30px {
    margin-block: 30px;
  }
  .my-60px {
    margin-block: 60px;
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-2\.5 {
    margin-top: calc(var(--spacing) * 2.5);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-9 {
    margin-top: 36px;
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-4\.5 {
    margin-bottom: 18px;
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: 32px;
  }
  .mb-9 {
    margin-bottom: 36px;
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-members-card {
    aspect-ratio: 142/165;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: 32px;
  }
  .h-9 {
    height: 36px;
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-50 {
    height: calc(var(--spacing) * 50);
  }
  .h-75 {
    height: calc(var(--spacing) * 75);
  }
  .h-100 {
    height: calc(var(--spacing) * 100);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .min-h-380 {
    min-height: 380px;
  }
  .scrollbar-thin {
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 100vh;
    }
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 100vh;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.4);
    }
  }
  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: 32px;
  }
  .w-9 {
    width: 36px;
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-115px {
    width: 115px;
  }
  .w-180px {
    width: 180px;
  }
  .w-auto {
    width: auto;
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-none {
    max-width: none;
  }
  .min-w-auto {
    min-width: auto;
  }
  .min-w-fit {
    min-width: fit-content;
  }
  .min-w-full {
    min-width: 100%;
  }
  .min-w-max {
    min-width: max-content;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .rotate-y-0 {
    --tw-rotate-y: rotateY(0deg);
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .rotate-y-180 {
    --tw-rotate-y: rotateY(180deg);
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .cursor-grab {
    cursor: grab;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .resize-none {
    resize: none;
  }
  .list-none {
    list-style-type: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .content-center {
    align-content: center;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-3\.5 {
    gap: calc(var(--spacing) * 3.5);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-4\.5 {
    gap: 18px;
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-7\.5 {
    gap: calc(var(--spacing) * 7.5);
  }
  .gap-8 {
    gap: 32px;
  }
  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }
  .gap-20 {
    gap: calc(var(--spacing) * 20);
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }
  .gap-x-8 {
    column-gap: 32px;
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-10 {
    row-gap: calc(var(--spacing) * 10);
  }
  .self-baseline {
    align-self: baseline;
  }
  .self-end {
    align-self: flex-end;
  }
  .self-start {
    align-self: flex-start;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-full-99 {
    border-radius: 99px;
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .border-\[var\(--color-blue-600\)\] {
    border-color: var(--color-blue-600);
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-border-main {
    border-color: var(--color-border-main);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-white-30 {
    border-color: rgba(255, 255, 255, 0.3);
  }
  .bg-background-main\/75 {
    background-color: color-mix(in srgb, #fafafa 75%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-background-main) 75%, transparent);
    }
  }
  .bg-background-navbar {
    background-color: var(--color-background-navbar);
  }
  .bg-background-other {
    background-color: var(--color-background-other);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-border-main {
    background-color: var(--color-border-main);
  }
  .bg-error {
    background-color: var(--color-error);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-lavender-100 {
    background-color: var(--color-lavender-100);
  }
  .bg-pink-600\/35 {
    background-color: color-mix(in srgb, #cc2e77 35%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-pink-600) 35%, transparent);
    }
  }
  .bg-red-400\/35 {
    background-color: color-mix(in srgb, #ef3d43 35%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-red-400) 35%, transparent);
    }
  }
  .bg-red-700 {
    background-color: var(--color-red-700);
  }
  .bg-text-main {
    background-color: var(--color-text-main);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white-30 {
    background-color: rgba(255, 255, 255, 0.3);
  }
  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }
  .bg-yellow-700\/35 {
    background-color: color-mix(in srgb, #e9b310 35%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-yellow-700) 35%, transparent);
    }
  }
  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-active-language {
    background-image: linear-gradient(180deg, #2B7AF1 0%, #333C8C 100%);
  }
  .bg-section-header {
    background-image: linear-gradient(158.02deg, #333138 13.83%, #28262C 85.62%);
  }
  .from-black\/20 {
    --tw-gradient-from: color-mix(in srgb, #000 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: 32px;
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .\!py-2 {
    padding-block: calc(var(--spacing) * 2) !important;
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-60px {
    padding-block: 60px;
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-15 {
    padding-top: calc(var(--spacing) * 15);
  }
  .pt-24 {
    padding-top: calc(var(--spacing) * 24);
  }
  .pt-124 {
    padding-top: 124px;
  }
  .pr-5 {
    padding-right: calc(var(--spacing) * 5);
  }
  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }
  .pr-20 {
    padding-right: calc(var(--spacing) * 20);
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }
  .pb-7\.5 {
    padding-bottom: calc(var(--spacing) * 7.5);
  }
  .pb-34 {
    padding-bottom: 34px;
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }
  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }
  .pl-20 {
    padding-left: calc(var(--spacing) * 20);
  }
  .text-center {
    text-align: center;
  }
  .body-1 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-body-1);
    line-height: var(--line-height-normal);
    font-weight: 400;
  }
  .body-2 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-body-2);
    line-height: var(--line-height-normal);
    font-weight: 400;
  }
  .body-3 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-body-3);
    line-height: var(--line-height-normal);
    font-weight: 400;
  }
  .btn-text {
    font-family: var(--font-mont);
    font-size: var(--font-size-button);
    line-height: var(--line-height-normal);
    font-weight: 600;
  }
  .display-1 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-display-1);
    line-height: var(--line-height-tight);
    font-weight: 700;
  }
  .heading-1 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-heading-1);
    line-height: var(--line-height-tight);
    font-weight: 700;
  }
  .heading-3 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-heading-3);
    line-height: var(--line-height-tight);
    font-weight: 700;
  }
  .heading-3-mobile {
    font-family: var(--font-agenda);
    font-size: var(--font-size-heading-3-mobile);
    line-height: var(--line-height-tight);
    font-weight: 700;
  }
  .link-text {
    font-family: var(--font-mont);
    font-size: var(--font-size-link);
    line-height: var(--line-height-normal);
    font-weight: 600;
  }
  .text-2 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-text-2);
    line-height: var(--line-height-normal);
    font-weight: 700;
  }
  .text-label {
    font-size: 0.5625rem;
    line-height: var(--tw-leading, 0.5625rem);
    font-weight: var(--tw-font-weight, 700);
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .break-all {
    word-break: break-all;
  }
  .text-\[var\(--color-blue-600\)\] {
    color: var(--color-blue-600);
  }
  .text-background-main {
    color: var(--color-background-main);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-black\/75 {
    color: color-mix(in srgb, #000 75%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-black) 75%, transparent);
    }
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-error {
    color: var(--color-error);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-pink-500 {
    color: var(--color-pink-500);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-steel-600 {
    color: var(--color-steel-600);
  }
  .text-text-main {
    color: var(--color-text-main);
  }
  .text-text-main-lighter {
    color: var(--color-text-main-lighter);
  }
  .text-text-secondary {
    color: var(--color-text-secondary);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-white-30 {
    color: rgba(255, 255, 255, 0.3);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .no-underline {
    text-decoration-line: none;
  }
  .underline {
    text-decoration-line: underline;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur-100 {
    --tw-backdrop-blur: blur(100px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .\[clip-path\:polygon\(0_0\,100\%_0\,calc\(100\%-12px\)_100\%\,0_100\%\)\] {
    clip-path: polygon(0 0,100% 0,calc(100% - 12px) 100%,0 100%);
  }
  .\[current-page\:title\] {
    current-page: title;
  }
  .\[current-page\:url\] {
    current-page: url;
  }
  .\[current-user\:mail\] {
    current-user: mail;
  }
  .\[current-user\:uid\] {
    current-user: uid;
  }
  .\[node\:summary\] {
    node: summary;
  }
  .\[node\:title\] {
    node: title;
  }
  .\[node\:url\] {
    node: url;
  }
  .\[site\:login-url\] {
    site: login-url;
  }
  .\[site\:mail\] {
    site: mail;
  }
  .\[site\:name\] {
    site: name;
  }
  .\[site\:url-brief\] {
    site: url-brief;
  }
  .\[site\:url\] {
    site: url;
  }
  .\[term\:description\] {
    term: description;
  }
  .\[term\:name\] {
    term: name;
  }
  .\[term\:url\] {
    term: url;
  }
  .\[user\:account-name\] {
    user: account-name;
  }
  .\[user\:cancel-url\] {
    user: cancel-url;
  }
  .\[user\:display-name\] {
    user: display-name;
  }
  .\[user\:edit-url\] {
    user: edit-url;
  }
  .\[user\:name\] {
    user: name;
  }
  .\[user\:one-time-login-url\] {
    user: one-time-login-url;
  }
  .\[user\:url\] {
    user: url;
  }
  .\[webform\:title\] {
    webform: title;
  }
  .backface-hidden {
    backface-visibility: hidden;
  }
  .perspective-1000 {
    perspective: 1000px;
  }
  .group-hover\:block {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        display: block;
      }
    }
  }
  .group-hover\:bg-white-30 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
  }
  .group-hover\:text-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-hover\/submenu\:text-white {
    &:is(:where(.group\/submenu):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .invalid\:border {
    &:invalid {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .invalid\:border-error {
    &:invalid {
      border-color: var(--color-error);
    }
  }
  .invalid\:text-error {
    &:invalid {
      color: var(--color-error);
    }
  }
  .invalid\:placeholder\:text-error {
    &:invalid {
      &::placeholder {
        color: var(--color-error);
      }
    }
  }
  .hover\:border-\[var\(--color-border-main\)\] {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-border-main);
      }
    }
  }
  .hover\:bg-blue-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-500);
      }
    }
  }
  .hover\:bg-blue-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-800);
      }
    }
  }
  .hover\:bg-white-30 {
    &:hover {
      @media (hover: hover) {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }
  }
  .hover\:bg-white\/30 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
        }
      }
    }
  }
  .hover\:text-\[var\(--color-text-main\)\] {
    &:hover {
      @media (hover: hover) {
        color: var(--color-text-main);
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-blue-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-700);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:border-blue-600 {
    &:focus {
      border-color: var(--color-blue-600);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-blue-600 {
    &:focus {
      --tw-ring-color: var(--color-blue-600);
    }
  }
  .focus\:ring-blue-700 {
    &:focus {
      --tw-ring-color: var(--color-blue-700);
    }
  }
  .focus\:ring-gray-500 {
    &:focus {
      --tw-ring-color: var(--color-gray-500);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .focus-visible\:ring-2 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-white {
    &:focus-visible {
      --tw-ring-color: var(--color-white);
    }
  }
  .md\:top-0 {
    @media (width >= 768px) {
      top: calc(var(--spacing) * 0);
    }
  }
  .md\:right-0 {
    @media (width >= 768px) {
      right: calc(var(--spacing) * 0);
    }
  }
  .md\:col-span-1 {
    @media (width >= 768px) {
      grid-column: span 1 / span 1;
    }
  }
  .md\:col-span-2 {
    @media (width >= 768px) {
      grid-column: span 2 / span 2;
    }
  }
  .md\:col-span-3 {
    @media (width >= 768px) {
      grid-column: span 3 / span 3;
    }
  }
  .md\:col-span-4 {
    @media (width >= 768px) {
      grid-column: span 4 / span 4;
    }
  }
  .md\:col-span-8 {
    @media (width >= 768px) {
      grid-column: span 8 / span 8;
    }
  }
  .md\:col-span-9 {
    @media (width >= 768px) {
      grid-column: span 9 / span 9;
    }
  }
  .md\:col-span-10 {
    @media (width >= 768px) {
      grid-column: span 10 / span 10;
    }
  }
  .md\:col-span-12 {
    @media (width >= 768px) {
      grid-column: span 12 / span 12;
    }
  }
  .md\:-mt-52 {
    @media (width >= 768px) {
      margin-top: calc(var(--spacing) * -52);
    }
  }
  .md\:-mt-64 {
    @media (width >= 768px) {
      margin-top: calc(var(--spacing) * -64);
    }
  }
  .md\:mb-0 {
    @media (width >= 768px) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:mb-4 {
    @media (width >= 768px) {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }
  .md\:mb-10 {
    @media (width >= 768px) {
      margin-bottom: calc(var(--spacing) * 10);
    }
  }
  .md\:mb-12 {
    @media (width >= 768px) {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }
  .md\:mb-15 {
    @media (width >= 768px) {
      margin-bottom: calc(var(--spacing) * 15);
    }
  }
  .md\:block {
    @media (width >= 768px) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 768px) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 768px) {
      display: none;
    }
  }
  .md\:inline-block {
    @media (width >= 768px) {
      display: inline-block;
    }
  }
  .md\:h-10 {
    @media (width >= 768px) {
      height: calc(var(--spacing) * 10);
    }
  }
  .md\:h-75 {
    @media (width >= 768px) {
      height: calc(var(--spacing) * 75);
    }
  }
  .md\:h-95 {
    @media (width >= 768px) {
      height: calc(var(--spacing) * 95);
    }
  }
  .md\:min-h-88\.5 {
    @media (width >= 768px) {
      min-height: calc(var(--spacing) * 88.5);
    }
  }
  .md\:w-1\/2 {
    @media (width >= 768px) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-10 {
    @media (width >= 768px) {
      width: calc(var(--spacing) * 10);
    }
  }
  .md\:w-115px {
    @media (width >= 768px) {
      width: 115px;
    }
  }
  .md\:w-auto {
    @media (width >= 768px) {
      width: auto;
    }
  }
  .md\:w-content-7 {
    @media (width >= 768px) {
      width: 747px;
    }
  }
  .md\:max-w-250 {
    @media (width >= 768px) {
      max-width: calc(var(--spacing) * 250);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 768px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 768px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 768px) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-6 {
    @media (width >= 768px) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-12 {
    @media (width >= 768px) {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 768px) {
      flex-direction: row;
    }
  }
  .md\:items-center {
    @media (width >= 768px) {
      align-items: center;
    }
  }
  .md\:gap-0 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 0);
    }
  }
  .md\:gap-1\.5 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 1.5);
    }
  }
  .md\:gap-2 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 2);
    }
  }
  .md\:gap-4 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .md\:gap-6 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .md\:gap-8 {
    @media (width >= 768px) {
      gap: 32px;
    }
  }
  .md\:gap-10 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 10);
    }
  }
  .md\:gap-12 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 12);
    }
  }
  .md\:gap-16 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 16);
    }
  }
  .md\:gap-25 {
    @media (width >= 768px) {
      gap: calc(var(--spacing) * 25);
    }
  }
  .md\:rounded-none {
    @media (width >= 768px) {
      border-radius: 0;
    }
  }
  .md\:bg-section-header {
    @media (width >= 768px) {
      background-image: linear-gradient(158.02deg, #333138 13.83%, #28262C 85.62%);
    }
  }
  .md\:p-5 {
    @media (width >= 768px) {
      padding: calc(var(--spacing) * 5);
    }
  }
  .md\:p-6 {
    @media (width >= 768px) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:p-7\.5 {
    @media (width >= 768px) {
      padding: calc(var(--spacing) * 7.5);
    }
  }
  .md\:py-0 {
    @media (width >= 768px) {
      padding-block: calc(var(--spacing) * 0);
    }
  }
  .md\:py-52px {
    @media (width >= 768px) {
      padding-block: 52px;
    }
  }
  .md\:py-60px {
    @media (width >= 768px) {
      padding-block: 60px;
    }
  }
  .md\:pt-67 {
    @media (width >= 768px) {
      padding-top: 67px;
    }
  }
  .md\:pr-6 {
    @media (width >= 768px) {
      padding-right: calc(var(--spacing) * 6);
    }
  }
  .md\:pb-15 {
    @media (width >= 768px) {
      padding-bottom: calc(var(--spacing) * 15);
    }
  }
  .md\:pb-60px {
    @media (width >= 768px) {
      padding-bottom: 60px;
    }
  }
  .md\:pl-8 {
    @media (width >= 768px) {
      padding-left: 32px;
    }
  }
  .md\:text-left {
    @media (width >= 768px) {
      text-align: left;
    }
  }
  .md\:body-1 {
    @media (width >= 768px) {
      font-family: var(--font-agenda);
      font-size: var(--font-size-body-1);
      line-height: var(--line-height-normal);
      font-weight: 400;
    }
  }
  .md\:body-2 {
    @media (width >= 768px) {
      font-family: var(--font-agenda);
      font-size: var(--font-size-body-2);
      line-height: var(--line-height-normal);
      font-weight: 400;
    }
  }
  .md\:heading-3 {
    @media (width >= 768px) {
      font-family: var(--font-agenda);
      font-size: var(--font-size-heading-3);
      line-height: var(--line-height-tight);
      font-weight: 700;
    }
  }
  .md\:text-1 {
    @media (width >= 768px) {
      font-family: var(--font-agenda);
      font-size: var(--font-size-text-1);
      line-height: var(--line-height-normal);
      font-weight: 700;
    }
  }
  .md\:text-background-main {
    @media (width >= 768px) {
      color: var(--color-background-main);
    }
  }
  .md\:opacity-0 {
    @media (width >= 768px) {
      opacity: 0%;
    }
  }
  .lg\:absolute {
    @media (width >= 1024px) {
      position: absolute;
    }
  }
  .lg\:relative {
    @media (width >= 1024px) {
      position: relative;
    }
  }
  .lg\:top-full {
    @media (width >= 1024px) {
      top: 100%;
    }
  }
  .lg\:right-0 {
    @media (width >= 1024px) {
      right: calc(var(--spacing) * 0);
    }
  }
  .lg\:bottom-auto {
    @media (width >= 1024px) {
      bottom: auto;
    }
  }
  .lg\:left-auto {
    @media (width >= 1024px) {
      left: auto;
    }
  }
  .lg\:z-10 {
    @media (width >= 1024px) {
      z-index: 10;
    }
  }
  .lg\:z-auto {
    @media (width >= 1024px) {
      z-index: auto;
    }
  }
  .lg\:mt-0\.5 {
    @media (width >= 1024px) {
      margin-top: calc(var(--spacing) * 0.5);
    }
  }
  .lg\:mt-1\.5 {
    @media (width >= 1024px) {
      margin-top: calc(var(--spacing) * 1.5);
    }
  }
  .lg\:mr-1 {
    @media (width >= 1024px) {
      margin-right: calc(var(--spacing) * 1);
    }
  }
  .lg\:block {
    @media (width >= 1024px) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 1024px) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 1024px) {
      display: none;
    }
  }
  .lg\:w-auto {
    @media (width >= 1024px) {
      width: auto;
    }
  }
  .lg\:w-full {
    @media (width >= 1024px) {
      width: 100%;
    }
  }
  .lg\:min-w-44 {
    @media (width >= 1024px) {
      min-width: calc(var(--spacing) * 44);
    }
  }
  .lg\:flex-grow {
    @media (width >= 1024px) {
      flex-grow: 1;
    }
  }
  .lg\:grid-cols-1 {
    @media (width >= 1024px) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 1024px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 1024px) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:flex-col {
    @media (width >= 1024px) {
      flex-direction: column;
    }
  }
  .lg\:flex-row {
    @media (width >= 1024px) {
      flex-direction: row;
    }
  }
  .lg\:items-center {
    @media (width >= 1024px) {
      align-items: center;
    }
  }
  .lg\:justify-center {
    @media (width >= 1024px) {
      justify-content: center;
    }
  }
  .lg\:gap-0\.5 {
    @media (width >= 1024px) {
      gap: calc(var(--spacing) * 0.5);
    }
  }
  .lg\:gap-1 {
    @media (width >= 1024px) {
      gap: calc(var(--spacing) * 1);
    }
  }
  .lg\:rounded-lg {
    @media (width >= 1024px) {
      border-radius: var(--radius-lg);
    }
  }
  .lg\:border-border-main {
    @media (width >= 1024px) {
      border-color: var(--color-border-main);
    }
  }
  .lg\:bg-blue-600 {
    @media (width >= 1024px) {
      background-color: var(--color-blue-600);
    }
  }
  .lg\:bg-text-main {
    @media (width >= 1024px) {
      background-color: var(--color-text-main);
    }
  }
  .lg\:bg-transparent {
    @media (width >= 1024px) {
      background-color: transparent;
    }
  }
  .lg\:bg-white {
    @media (width >= 1024px) {
      background-color: var(--color-white);
    }
  }
  .lg\:p-0 {
    @media (width >= 1024px) {
      padding: calc(var(--spacing) * 0);
    }
  }
  .lg\:p-4 {
    @media (width >= 1024px) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .lg\:p-7 {
    @media (width >= 1024px) {
      padding: calc(var(--spacing) * 7);
    }
  }
  .lg\:px-0 {
    @media (width >= 1024px) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:px-1\.5 {
    @media (width >= 1024px) {
      padding-inline: calc(var(--spacing) * 1.5);
    }
  }
  .lg\:px-6 {
    @media (width >= 1024px) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .lg\:py-12 {
    @media (width >= 1024px) {
      padding-block: calc(var(--spacing) * 12);
    }
  }
  .lg\:text-black {
    @media (width >= 1024px) {
      color: var(--color-black);
    }
  }
  .lg\:text-text-main {
    @media (width >= 1024px) {
      color: var(--color-text-main);
    }
  }
  .lg\:text-white {
    @media (width >= 1024px) {
      color: var(--color-white);
    }
  }
  .lg\:shadow-admin-menu {
    @media (width >= 1024px) {
      --tw-shadow: 1px 1px 4px 0px var(--tw-shadow-color, rgba(0, 0, 0, 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .lg\:shadow-main-menu {
    @media (width >= 1024px) {
      --tw-shadow: 3px 2px 7px 0px var(--tw-shadow-color, rgba(0, 0, 0, 0.15));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .lg\:group-hover\:flex {
    @media (width >= 1024px) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          display: flex;
        }
      }
    }
  }
  .lg\:group-hover\:rotate-180 {
    @media (width >= 1024px) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          rotate: 180deg;
        }
      }
    }
  }
  .lg\:hover\:bg-blue-600 {
    @media (width >= 1024px) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-blue-600);
        }
      }
    }
  }
  .lg\:hover\:text-white {
    @media (width >= 1024px) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-white);
        }
      }
    }
  }
}
@layer base {
  html {
    scroll-behavior: smooth;
  }
  @font-face {
    font-family: "Agenda";
    src: url("./assets/fonts/agenda-regular.otf") format("opentype");
    font-weight: 400;
    font-display: swap;
  }
  @font-face {
    font-family: "Agenda";
    src: url("./assets/fonts/agenda-bold.otf") format("opentype");
    font-weight: 700;
    font-display: swap;
  }
  @font-face {
    font-family: "Mont";
    src: url("./assets/fonts/mont-semibold.otf") format("opentype");
    font-weight: 600;
    font-display: swap;
  }
  body {
    background-color: var(--color-white);
    font-family: var(--font-agenda);
    font-size: var(--font-size-body-2);
    line-height: var(--line-height-normal);
    font-weight: 400;
    @media (width >= 768px) {
      background-color: var(--color-background-main);
    }
  }
  h1 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-heading-1);
    line-height: var(--line-height-tight);
    font-weight: 700;
    @media (width >= 48rem) {
      font-family: var(--font-agenda);
      font-size: var(--font-size-heading-1-mobile);
      line-height: var(--line-height-tight);
      font-weight: 700;
    }
  }
  h2 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-heading-2);
    line-height: var(--line-height-tight);
    font-weight: 400;
    @media (width >= 48rem) {
      font-family: var(--font-agenda);
      font-size: var(--font-size-heading-2-mobile);
      line-height: var(--line-height-tight);
      font-weight: 400;
    }
  }
  h3 {
    font-family: var(--font-agenda);
    font-size: var(--font-size-heading-3);
    line-height: var(--line-height-tight);
    font-weight: 700;
    @media (width >= 48rem) {
      font-family: var(--font-agenda);
      font-size: var(--font-size-heading-3-mobile);
      line-height: var(--line-height-tight);
      font-weight: 700;
    }
  }
}
@layer components {
  .bwy-container {
    width: 100%;
    @media (width >= 640px) {
      max-width: 640px;
    }
    @media (width >= 768px) {
      max-width: 768px;
    }
    @media (width >= 1024px) {
      max-width: 1024px;
    }
    @media (width >= 1280px) {
      max-width: 1280px;
    }
    @media (width >= 1536px) {
      max-width: 1536px;
    }
    margin-inline: auto;
    padding-inline: calc(var(--spacing) * 6);
    @media (width >= 768px) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .link-primary {
    padding-block: calc(var(--spacing) * 1);
    font-family: var(--font-mont);
    font-size: var(--font-size-link);
    line-height: var(--line-height-normal);
    font-weight: 600;
    text-decoration-line: none;
    color: var(--color-blue-600);
  }
  .tw-hidden {
    display: none;
  }
  .card {
    overflow: hidden;
    border-radius: var(--radius-lg);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-border-main);
    background-color: color-mix(in srgb, #fafafa 25%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-background-main) 25%, transparent);
    }
    --tw-backdrop-blur: blur(100px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .card--hover {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 2px 3px 7px var(--tw-shadow-color, rgba(0, 0, 0, 0.07)), 7px 11px 13px var(--tw-shadow-color, rgba(0, 0, 0, 0.06)), 17px 25px 18px var(--tw-shadow-color, rgba(0, 0, 0, 0.04)), 30px 45px 21px var(--tw-shadow-color, rgba(0, 0, 0, 0.01)), 46px 70px 23px var(--tw-shadow-color, rgba(0, 0, 0, 0));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .news-card--page {
    border-radius: var(--radius-2xl);
    border-style: var(--tw-border-style);
    border-width: 0px;
    background-color: color-mix(in srgb, #fafafa 75%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-background-main) 75%, transparent);
    }
    @media (width >= 768px) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
    @media (width >= 768px) {
      border-color: var(--color-border-main);
    }
  }
  .full-text {
    text-align: justify;
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
    p,
    h2,
    h3,
    h4,
    h5,
    h6 {
      padding-bottom: calc(var(--spacing) * 4);
    }
    h2,
    h3,
    h4,
    h5,
    h6 {
      padding-top: calc(var(--spacing) * 2);
      --tw-font-weight: var(--font-weight-bold);
      font-weight: var(--font-weight-bold);
    }
    h2 {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
      --tw-tracking: var(--tracking-wide);
      letter-spacing: var(--tracking-wide);
      text-transform: uppercase;
    }
    h3 {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
    h4 {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
    h5 {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
    h6 {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
    ul,
    ol {
      list-style-position: inside;
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
    ul {
      list-style-type: disc;
    }
    ol {
      list-style-type: decimal;
    }
    ul li,
    ol li {
      text-indent: calc(var(--spacing) * 10);
    }
    blockquote {
      margin-block: calc(var(--spacing) * 6);
      border-left-style: var(--tw-border-style);
      border-left-width: 2px;
      border-color: var(--color-text-main);
      padding-left: calc(var(--spacing) * 4);
      font-style: italic;
    }
    img {
      margin-inline: auto;
      margin-block: 32px;
      width: 100%;
      border-radius: var(--radius-lg);
    }
    figure {
      margin-block: 32px;
    }
    figcaption {
      margin-top: calc(var(--spacing) * 2);
      border-left-style: var(--tw-border-style);
      border-left-width: 2px;
      border-color: var(--color-text-main);
      padding-left: calc(var(--spacing) * 4);
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
      font-style: italic;
    }
  }
  .ck-editor {
    ul,
    ol {
      list-style-position: inside;
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
    ul {
      list-style-type: disc;
    }
    ol {
      list-style-type: decimal;
    }
    ul li,
    ol li {
      text-indent: calc(var(--spacing) * 10);
      .ck-list-bogus-paragraph {
        display: inline !important;
      }
    }
  }
  .heading-underline {
    &::after {
      content: var(--tw-content);
      margin-top: calc(var(--spacing) * 2);
    }
    &::after {
      content: var(--tw-content);
      display: block;
    }
    &::after {
      content: var(--tw-content);
      height: 1px;
    }
    &::after {
      content: var(--tw-content);
      width: 36px;
    }
    &::after {
      content: var(--tw-content);
      background-color: var(--color-border-main);
    }
    &::after {
      content: var(--tw-content);
      --tw-content: '';
      content: var(--tw-content);
    }
  }
  .field--name-field-media-image img {
    width: 100%;
  }
}
.toc-js-container nav {
  a {
    position: relative;
    margin-bottom: calc(var(--spacing) * 3);
    display: block;
    padding-left: calc(var(--spacing) * 6);
    color: var(--color-text-main-lighter);
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
    &::before {
      content: "";
      mask-image: url("../src/svg/chevron_right_filled.svg");
      mask-repeat: no-repeat;
      mask-position: center;
      position: absolute;
      top: calc(var(--spacing) * 0);
      left: calc(var(--spacing) * 0);
      height: calc(var(--spacing) * 5);
      width: calc(var(--spacing) * 5);
      background-color: var(--color-text-main-lighter);
      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-duration: 200ms;
      transition-duration: 200ms;
    }
    &:hover::before {
      background-color: var(--color-blue-600);
    }
  }
  .toc-active {
    > a {
      --tw-font-weight: var(--font-weight-bold);
      font-weight: var(--font-weight-bold);
      color: var(--color-blue-600);
      &::before {
        background-color: var(--color-blue-600);
      }
    }
  }
}
.block-private-message-inbox-block {
  .private-message-thread {
    position: relative;
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    border-left-style: var(--tw-border-style);
    border-left-width: 3px;
    border-color: var(--color-white);
  }
  .active-thread {
    border-color: var(--color-blue-600);
    background-color: var(--color-border-main);
  }
  .unread-thread {
    .unread-dot {
      background-color: var(--color-blue-600);
    }
  }
}
#load-previous-messages-button-wrapper {
  margin-block: calc(var(--spacing) * 4);
  display: block;
  text-align: center;
  a {
    font-family: var(--font-agenda);
    font-size: var(--font-size-body-2);
    line-height: var(--line-height-normal);
    font-weight: 400;
  }
}
.ajax-progress {
  position: absolute;
  top: calc(var(--spacing) * 0);
}
@layer utilities {
  .content-empty {
    content: "";
  }
}
.body-no-scroll {
  overflow: hidden;
  @media (width >= 1024px) {
    overflow: auto;
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-tracking: initial;
      --tw-content: "";
    }
  }
}
