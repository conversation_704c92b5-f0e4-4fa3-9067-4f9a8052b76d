import plugin from 'tailwindcss/plugin';

export default {
  content: [
    "./*.theme",
    "./templates/**/*.html.twig",
    "./components/**/*.twig",
    "./components/**/*.stories.json",
    "./components/**/*.stories.yml",
    "../../../../config/**/*.yml",
    "../../../modules/custom/**/*.html.twig",
    "../../../modules/custom/**/*.js",
    "../../../modules/custom/**/*.php",
    "../../../modules/custom/**/*.yml",
    "../../../modules/custom/**/*.module",
    "../../../modules/custom/**/*.inc",
  ],
  theme: {
    extend: {
      spacing: {
        '4.5': '18px',
        '8': '32px',
        '9': '36px',
        '30px': '30px',
        '60px': '60px',
        '52px': '52px',
      },
      zIndex: {
        '1': '1',
      },
      perspective: {
        '1000': '1000px',
      },
      aspectRatio: {
        'members-card': '142/165',
      },
      borderRadius: {
        'full-99': '99px',
      },
      backgroundImage: {
        'section-header': 'linear-gradient(158.02deg, #333138 13.83%, #28262C 85.62%)',
        'active-language': 'linear-gradient(180deg, #2B7AF1 0%, #333C8C 100%)',

      },
      minHeight: {
        '380': '380px',
      },
      fontSize: {
        'label': ['0.5625rem', {
          lineHeight: '0.5625rem',
          fontWeight: '700',
        }],
      },
      colors: {
        'white-30': 'rgba(255, 255, 255, 0.3)',
        'text-main-lighter': 'rgba(51, 49, 56, 0.65)',
      },
      padding: {
        '67': '67px',
        '34': '34px',
        '60px': '60px',
        '124': '124px',
      },
      width: {
        'content-7': '747px',
        '36px': '36px',
        '115px': '115px',
        '180px': '180px',
      },
      backdropBlur: {
        '100': '100px',
      },
      boxShadow: {
        'main-menu': '3px 2px 7px 0px rgba(0, 0, 0, 0.15)',
        'admin-menu': '1px 1px 4px 0px rgba(0, 0, 0, 0.1)',
      }
    },
    screens: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
  },
  plugins: [
    plugin(function({ addUtilities }) {
      addUtilities({
        '.scrollbar-thin': {
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'rgba(0, 0, 0, 0.1)',
            borderRadius: '100vh',
          },
          '&::-webkit-scrollbar-thumb': {
            background: 'rgba(0, 0, 0, 0.3)',
            borderRadius: '100vh',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: 'rgba(0, 0, 0, 0.4)',
          },
        },
      });
    }),
  ],
};
