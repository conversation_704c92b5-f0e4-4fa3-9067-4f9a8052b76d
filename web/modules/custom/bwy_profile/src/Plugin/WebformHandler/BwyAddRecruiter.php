<?php

namespace Drupal\bwy_profile\Plugin\WebformHandler;

use <PERSON><PERSON><PERSON>\webform\Plugin\WebformHandlerBase;
use <PERSON><PERSON><PERSON>\webform\WebformSubmissionInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use <PERSON><PERSON><PERSON>\user\Entity\User;
use Drupal\profile\Entity\Profile;
use <PERSON><PERSON><PERSON>\group\Entity\Group;

/**
 * Webform handler to create a new recruiter user and add them to a group.
 *
 * @WebformHandler(
 *   id = "bwy_add_recruiter_user",
 *   label = @Translation("Add Recruiter User"),
 *   category = @Translation("Development"),
 *   description = @Translation("Creates a new recruiter user and adds them to the group."),
 *   cardinality = \Drupal\webform\Plugin\WebformHandlerInterface::CARDINALITY_SINGLE,
 *   results = \Drupal\webform\Plugin\WebformHandlerInterface::RESULTS_PROCESSED,
 *   submission = \Drupal\webform\Plugin\WebformHandlerInterface::SUBMISSION_REQUIRED,
 * )
 */
class BwyAddRecruiter extends WebformHandlerBase {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountProxyInterface
   */
  protected $currentUser;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * The config factory.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $instance = parent::create($container, $configuration, $plugin_id, $plugin_definition);
    $instance->currentUser = $container->get('current_user');
    $instance->entityTypeManager = $container->get('entity_type.manager');
    $instance->messenger = $container->get('messenger');
    $instance->loggerFactory = $container->get('logger.factory');
    $instance->configFactory = $container->get('config.factory');
    $instance->renderer = $container->get('renderer');
    return $instance;
  }

  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return [
      'states' => [WebformSubmissionInterface::STATE_COMPLETED],
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function postSave(WebformSubmissionInterface $webform_submission, $update = TRUE) {
    $state = $webform_submission->getWebform()->getSetting('results_disabled')
      ? WebformSubmissionInterface::STATE_COMPLETED
      : $webform_submission->getState();

    if (!in_array($state, $this->configuration['states'])) {
      return;
    }

    try {
      $values = $webform_submission->getData();
      $this->validateRequiredFields($values);
      $group = $this->getAdminGroup();
      $this->validateUserNotInAnyGroup($values['email']);
      $this->validateUserDoesNotExist($values['email']);

      $user = $this->createUser($values);
      $this->createProfile($user, $values);
      $this->addUserToGroup($group, $user, $values);
      $this->sendWelcomeEmail($user);

      $this->logSuccess($values['email'], $group);

    }
    catch (\Exception $e) {
      $this->handleError($e);
    }
  }

  /**
   * Validates that all required fields are present.
   *
   * @param array $values
   *   The form submission values.
   *
   * @throws \Exception
   *   If required fields are missing.
   */
  protected function validateRequiredFields(array $values): void {
    if (empty($values['email']) || empty($values['first_name']) || empty($values['last_name']) || empty($values['role'])) {
      throw new \Exception('Required fields are missing.');
    }
  }

  /**
   * Gets the group where the current user is an admin.
   *
   * @return \Drupal\group\Entity\Group
   *   The group entity.
   *
   * @throws \Exception
   *   If no admin group is found.
   */
  protected function getAdminGroup(): Group {
    $memberships = $this->entityTypeManager
      ->getStorage('group_relationship')
      ->loadByProperties([
        'entity_id' => $this->currentUser->id(),
        'plugin_id' => 'group_membership',
        'type' => 'company-group_membership',
      ]);
    if (empty($memberships)) {
      throw new \Exception('No group memberships found for the current user.');
    }

    /** @var \Drupal\group\Entity\GroupRelationship $membership */
    foreach ($memberships as $membership) {
      $roles = array_column($membership->get('group_roles')->getValue(), 'target_id');
      if (in_array('company-admin', $roles, TRUE)) {
        return $membership->getGroup();
      }
    }

    throw new \Exception('No group found where the current user is a company admin.');
  }

  /**
   * Validates that a user with the given email doesn't exist.
   *
   * @param string $email
   *   The email to check.
   *
   * @throws \Exception
   *   If a user with the email already exists.
   */
  protected function validateUserDoesNotExist(string $email): void {
    if (user_load_by_mail($email)) {
      throw new \Exception('A user with this email already exists.');
    }
  }

  /**
   * Validates that a user with the given email doesn't exist in any group.
   *
   * @param string $email
   *   The email to check.
   *
   * @throws \Exception
   *   If a user with the email already exists in any group.
   */
  protected function validateUserNotInAnyGroup(string $email): void {
    // First check if user exists.
    $user = user_load_by_mail($email);
    if ($user) {
      // Check if user is a member of any group.
      $group_memberships = $this->entityTypeManager
        ->getStorage('group_relationship')
        ->loadByProperties([
          'entity_id' => $user->id(),
          'plugin_id' => 'group_membership',
        ]);

      if (!empty($group_memberships)) {
        throw new \Exception('User with this email is already a member of a group.');
      }
    }
  }

  /**
   * Creates a new user.
   *
   * @param array $values
   *   The form submission values.
   *
   * @return \Drupal\user\Entity\User
   *   The created user entity.
   */
  protected function createUser(array $values): User {
    $role = $values['role'][0] === 'company_admin' ? 'company_admin' : 'company_recruiter';

    $user = User::create([
      'name' => $values['email'],
      'mail' => $values['email'],
      'status' => 1,
      'roles' => [$role],
    ]);
    $user->save();
    return $user;
  }

  /**
   * Creates a profile for the user.
   *
   * @param \Drupal\user\Entity\User $user
   *   The user entity.
   * @param array $values
   *   The form submission values.
   */
  protected function createProfile(User $user, array $values): void {
    $profile_type = $values['role'][0] === 'company_admin' ? 'company_admin' : 'recruiter';
    $profile = Profile::create([
      'type' => $profile_type,
      'uid' => $user->id(),
      'field_first_name' => $values['first_name'],
      'field_last_name' => $values['last_name'],
    ]);
    $profile->save();
  }

  /**
   * Adds a user to a group.
   *
   * @param \Drupal\group\Entity\Group $group
   *   The group entity.
   * @param \Drupal\user\Entity\User $user
   *   The user entity.
   * @param array $values
   *   The form submission values.
   */
  protected function addUserToGroup(Group $group, User $user, array $values): void {
    $group_role = $values['role'][0] === 'company_admin' ? 'company-admin' : 'company-recruiter';
    $group->addMember($user, ['group_roles' => [$group_role]]);
  }

  /**
   * Sends a welcome email to the user.
   *
   * @param \Drupal\user\Entity\User $user
   *   The user entity.
   */
  protected function sendWelcomeEmail(User $user): void {
    _user_mail_notify('register_no_approval_required', $user);
  }

  /**
   * Logs a success message.
   *
   * @param string $email
   *   The email of the created user.
   * @param \Drupal\group\Entity\Group $group
   *   The group the user was added to.
   */
  protected function logSuccess(string $email, Group $group): void {
    $this->loggerFactory->get('bwy_profile')->notice('Created new member @email and added to group @group', [
      '@email' => $email,
      '@group' => $group->label(),
    ]);
  }

  /**
   * Handles errors by logging and displaying them.
   *
   * @param \Exception $e
   *   The exception to handle.
   */
  protected function handleError(\Exception $e): void {
    $this->loggerFactory->get('bwy_profile')->error('Failed to add a member: @error', [
      '@error' => $e->getMessage(),
    ]);

    $this->messenger->addError($this->t('Failed to add a member: @error', [
      '@error' => $e->getMessage(),
    ]));
  }

}
